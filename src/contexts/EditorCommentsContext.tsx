import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from "react";
import { Comment } from "@/components/collaboration/types";
import { useAuth } from "@/contexts/AuthContext";
import { api } from "@/integrations/legal-concierge/api";
import { toast } from "sonner";

interface EditorCommentsContextType {
  comments: Comment[];
  isLoading: boolean;
  error: string | null;
  documentId: string | null;

  // Comment management
  addComment: (commentData: {
    content: string;
    position?: { x: number; y: number };
  }) => Promise<Comment>;
  resolveComment: (commentId: string) => Promise<void>;
  refreshComments: () => Promise<void>;
  setDocumentId: (documentId: string | null) => void;
  clearError: () => void;

  // Editor integration
  highlightComment: (commentId: string) => void;
  activeCommentId: string | null;
  setActiveCommentId: (commentId: string | null) => void;
}

const EditorCommentsContext = createContext<EditorCommentsContextType | null>(
  null
);

export const useEditorComments = () => {
  const context = useContext(EditorCommentsContext);
  if (!context) {
    throw new Error(
      "useEditorComments must be used within EditorCommentsProvider"
    );
  }
  return context;
};

interface EditorCommentsProviderProps {
  children: React.ReactNode;
  initialDocumentId?: string;
}

export const EditorCommentsProvider: React.FC<EditorCommentsProviderProps> = ({
  children,
  initialDocumentId,
}) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documentId, setDocumentId] = useState<string | null>(
    initialDocumentId || null
  );
  const [activeCommentId, setActiveCommentId] = useState<string | null>(null);
  const { user } = useAuth();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshComments = useCallback(async () => {
    if (!documentId || !user?.companyId) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await api.getDocumentComments(
        user.companyId,
        documentId
      );

      if (response.error) {
        throw new Error(response.error);
      }

      // Ensure response.data is an array
      const commentsData = Array.isArray(response.data)
        ? response.data
        : [response.data].filter(Boolean);

      // Transform the API response to match our Comment type
      const formattedComments: Comment[] = commentsData.map((comment) => {
        const timestamp = (() => {
          try {
            const date = new Date(comment.timestamp || comment.createdAt);
            return isNaN(date.getTime()) ? new Date() : date;
          } catch {
            return new Date();
          }
        })();

        return {
          id: comment.id,
          userId: comment.userId,
          userName: comment.userName || comment.userId || "Anonymous",
          documentId: comment.documentIdentifier || documentId,
          content: comment.comment,
          timestamp,
          position: comment.position,
          resolved: comment.resolved,
          statusText: comment.statusText,
        };
      });

      setComments(formattedComments);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to load comments";
      console.error("Error fetching comments:", error);
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [documentId, user?.companyId]);

  const addComment = useCallback(
    async (commentData: {
      content: string;
      position?: { x: number; y: number };
    }): Promise<Comment> => {
      if (!documentId || !user?.companyId) {
        throw new Error("Document ID or company ID not available");
      }

      const response = await api.createDocumentComment(
        user.companyId,
        documentId,
        {
          comment: commentData.content,
          position: commentData.position,
          resolved: false,
        }
      );

      if (response.error || !response.data) {
        throw new Error(response.error || "Failed to create comment");
      }

      const timestamp = (() => {
        try {
          const date = new Date(response.data.createdAt);
          return isNaN(date.getTime()) ? new Date() : date;
        } catch {
          return new Date();
        }
      })();

      const newComment: Comment = {
        id: response.data.id,
        userId: response.data.userId,
        userName:
          response.data.userName ||
          user.fullName ||
          user.email?.split("@")[0] ||
          "Anonymous",
        documentId: response.data.documentIdentifier,
        content: response.data.comment,
        timestamp,
        position: response.data.position,
        resolved: response.data.resolved,
        statusText: response.data.statusText,
      };

      setComments((prevComments) => [newComment, ...prevComments]);
      return newComment;
    },
    [documentId, user]
  );

  const resolveComment = useCallback(
    async (commentId: string) => {
      try {
        const comment = comments.find((c) => c.id === commentId);
        if (!comment) return;

        const response = await api.updateDocumentComment(commentId, {
          comment: comment.content,
          resolved: true,
        });

        if (response.error) {
          throw new Error(response.error);
        }

        setComments((prevComments) =>
          prevComments.map((comment) =>
            comment.id === commentId ? { ...comment, resolved: true } : comment
          )
        );
        toast.success("Comment resolved successfully");
      } catch (error) {
        console.error("Error resolving comment:", error);
        toast.error("Failed to resolve comment");
      }
    },
    [comments]
  );

  const highlightComment = useCallback((commentId: string) => {
    // Try different possible selectors for comment elements
    const possibleSelectors = [
      `[data-comment-id="${commentId}"]`,
      `[data-comment="${commentId}"]`,
      `[comment-id="${commentId}"]`,
      `.comment-highlight[data-id="${commentId}"]`,
      `.comment-highlight[id="${commentId}"]`,
    ];

    let commentElements: NodeListOf<Element> | null = null;
    let usedSelector = "";

    for (const selector of possibleSelectors) {
      commentElements = document.querySelectorAll(selector);
      if (commentElements.length > 0) {
        usedSelector = selector;
        break;
      }
    }

    console.log(`Looking for comment elements with ID: ${commentId}`, {
      usedSelector,
      commentElements,
    });

    // Also log all elements with comment-highlight class to see what attributes they have
    const allCommentElements = document.querySelectorAll(".comment-highlight");
    console.log("All comment highlight elements:", allCommentElements);
    allCommentElements.forEach((el, index) => {
      console.log(
        `Comment element ${index}:`,
        el,
        "Attributes:",
        el.attributes
      );
    });

    if (commentElements && commentElements.length > 0) {
      const firstElement = commentElements[0] as HTMLElement;
      console.log("Found comment element, scrolling to it:", firstElement);

      firstElement.scrollIntoView({ behavior: "smooth", block: "center" });

      // Temporarily highlight the comment
      firstElement.classList.add("active");
      setTimeout(() => {
        firstElement.classList.remove("active");
      }, 2000);
    } else {
      console.log("No comment elements found for ID:", commentId);
    }
    setActiveCommentId(commentId);
  }, []);

  // Fetch comments when documentId changes
  useEffect(() => {
    if (documentId) {
      refreshComments();
    } else {
      setComments([]);
    }
  }, [documentId, refreshComments]);

  const value: EditorCommentsContextType = {
    comments,
    isLoading,
    error,
    documentId,
    addComment,
    resolveComment,
    refreshComments,
    setDocumentId,
    clearError,
    highlightComment,
    activeCommentId,
    setActiveCommentId,
  };

  return (
    <EditorCommentsContext.Provider value={value}>
      {children}
    </EditorCommentsContext.Provider>
  );
};
