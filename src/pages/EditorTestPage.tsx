import React, { useState } from "react";
import { EditorWithComments } from "@/components/tiptap/EditorWithComments";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const EditorTestPage: React.FC = () => {
  const [documentId, setDocumentId] = useState("test-document-1");
  const [showCommentsPanel, setShowCommentsPanel] = useState(true);
  const [showDocumentComments, setShowDocumentComments] = useState(true);

  const initialContent = `
    <h1>Test Document</h1>
    <p>This is a test document for the integrated comment system. You can:</p>
    <ul>
      <li>Select text and create comments</li>
      <li>View comments in both panels</li>
      <li>Navigate between comments</li>
      <li>Resolve comments</li>
    </ul>
    <p>Try selecting some text and adding a comment!</p>
  `;

  return (
    <div className="h-screen flex flex-col">
      {/* Header Controls */}
      <div className="border-b border-gray-200 p-4 bg-white">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label htmlFor="documentId" className="text-sm font-medium">
              Document ID:
            </label>
            <Input
              id="documentId"
              value={documentId}
              onChange={(e) => setDocumentId(e.target.value)}
              className="w-48"
              placeholder="Enter document ID"
            />
          </div>
          
          <Button
            variant={showCommentsPanel ? "default" : "outline"}
            onClick={() => setShowCommentsPanel(!showCommentsPanel)}
            size="sm"
          >
            TipTap Comments Panel
          </Button>
          
          <Button
            variant={showDocumentComments ? "default" : "outline"}
            onClick={() => setShowDocumentComments(!showDocumentComments)}
            size="sm"
          >
            Document Comments Panel
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        <EditorWithComments
          initialContent={initialContent}
          documentId={documentId}
          showCommentsPanel={showCommentsPanel}
          showDocumentComments={showDocumentComments}
        />
      </div>

      {/* Instructions */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">How to Test</CardTitle>
          </CardHeader>
          <CardContent className="text-xs space-y-1">
            <p>1. Select text in the editor and create a comment</p>
            <p>2. The comment will be saved to the backend with a server-generated ID</p>
            <p>3. The text will be highlighted with the proper data-comment-id attribute</p>
            <p>4. Comments appear in both the TipTap panel (right overlay) and Document Comments panel (right column)</p>
            <p>5. Click comments in either panel to navigate to the highlighted text</p>
            <p>6. Resolve comments to test the full workflow</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditorTestPage;
