import { useEffect } from "react";
import { BrowserRouter, Route, Routes, useLocation } from "react-router-dom";
import "./App.css";

// Pages
import About from "./pages/About";
import CapTable from "./pages/CapTable";
import CompanyDocuments from "./pages/CompanyDocuments";
import Dashboard from "./pages/Dashboard";
import DataRoom from "./pages/DataRoom";
import Documents from "./pages/Documents";
import ExternalIncorporation from "./pages/ExternalIncorporation";
import ForgotPassword from "./pages/ForgotPassword";
import Index from "./pages/Index";
import Login from "./pages/Login";
import ManageDirectors from "./pages/ManageDirectors";
import MfaVerification from "./pages/MfaVerification";
import NotFound from "./pages/NotFound";
import PostIncorporation from "./pages/PostIncorporation";
import Profile from "./pages/Profile";
import Questions from "./pages/Questions";
import ResetPassword from "./pages/ResetPassword";
import ReviewDocuments from "./pages/ReviewDocuments";
import ServiceProviders from "./pages/ServiceProviders";
import SignDocuments from "./pages/SignDocuments";
import SignerDashboard from "./pages/SignerDashboard";

// Components
import AuthRedirect from "./components/auth/AuthRedirect";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { Toaster } from "./components/ui/sonner";

// Context providers
import { USER_ROLES } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "./contexts/AuthContext";
import { CompanySelectionProvider } from "./contexts/CompanySelectionContext";
import DocumentViewer from "./pages/DocumentViewer";
import { TiptapPage } from "./pages/TiptapPage";
import EditorTestPage from "./pages/EditorTestPage";

// ScrollToTop component to reset scroll position on route change
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}
const queryClient = new QueryClient();

function App() {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <CompanySelectionProvider>
            <ScrollToTop />
            <Routes>
              {/* Public routes that don't require authentication */}
              <Route
                path="/"
                element={
                  <AuthRedirect>
                    <Index />
                  </AuthRedirect>
                }
              />
              <Route path="/home" element={<Index />} />
              <Route path="/documentviewer" element={<DocumentViewer />} />
              <Route path="/about" element={<About />} />
              <Route path="/login" element={<Login />} />
              <Route path="/tiptap" element={<TiptapPage />} />
              <Route path="/register" element={<Login />} />
              <Route path="/mfa" element={<MfaVerification />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />

              {/* Protected routes that require authentication */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                  >
                    <Dashboard />
                  </ProtectedRoute>
                }
              />

              {/* SIGNER-only dashboard */}
              <Route
                path="/signer-dashboard"
                element={
                  <ProtectedRoute allowedRoles={[USER_ROLES.SIGNER]}>
                    <SignerDashboard />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/questions"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <Questions />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/review-documents"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <ReviewDocuments />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/sign-documents"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <SignDocuments />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/post-incorporation"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER]}
                    redirectTo="/signer-dashboard"
                  >
                    <PostIncorporation />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/external-incorporation"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <ExternalIncorporation />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/cap-table"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <CapTable />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/data-room"
                element={
                  <ProtectedRoute
                    allowedRoles={[
                      USER_ROLES.OWNER,
                      USER_ROLES.COLLABORATOR,
                      USER_ROLES.SIGNER,
                    ]}
                  >
                    <DataRoom />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/documents"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <Documents />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/company-documents"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <CompanyDocuments />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/manage-directors"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <ManageDirectors />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/service-providers"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <ServiceProviders />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/profile"
                element={
                  <ProtectedRoute
                    allowedRoles={[USER_ROLES.OWNER, USER_ROLES.COLLABORATOR]}
                    redirectTo="/signer-dashboard"
                  >
                    <Profile />
                  </ProtectedRoute>
                }
              />

              <Route path="*" element={<NotFound />} />
            </Routes>
          </CompanySelectionProvider>
        </AuthProvider>
      </QueryClientProvider>
      <Toaster />
    </BrowserRouter>
  );
}

export default App;
