import React, { useContext, useEffect, useState, useCallback } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { DocumentCommentsContext } from "@/components/signature/DocumentSigningContainer";
import { useEditorComments } from "@/contexts/EditorCommentsContext";
import { Comment } from "./types";
import CommentsHeader from "./CommentsHeader";
import CommentList from "./CommentList";
import CommentForm from "./CommentForm";
import { useComments } from "./hooks/useComments";
import { useAuth } from "@/contexts/AuthContext";
import { api } from "@/integrations/legal-concierge/api";
import { toast } from "sonner";

interface DocumentCommentsProps {
  isDashboard?: boolean;
  loading?: boolean;
  useEditorIntegration?: boolean;
}

const DocumentComments: React.FC<DocumentCommentsProps> = ({
  isDashboard = false,
  loading: externalLoading,
  useEditorIntegration = false,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(externalLoading ?? true);
  const [companyComments, setCompanyComments] = useState<Comment[]>([]);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Get editor context if available (always call hook)
  const editorContext = useEditorComments();

  // Try to get comments from document context, if not available, use local state
  let contextComments: Comment[] | undefined;
  let contextAddComment:
    | ((data: {
        content: string;
        position?: { x: number; y: number };
      }) => Promise<void>)
    | undefined;
  let contextResolveComment: ((id: string) => Promise<void>) | undefined;

  try {
    const context = useContext(DocumentCommentsContext);
    if (!isDashboard && !useEditorIntegration) {
      contextComments = context?.comments;
      contextAddComment = context?.addComment;
      contextResolveComment = context?.resolveComment;
    }
  } catch (error) {
    // Context not available, will use local state instead
  }

  const fetchComments = useCallback(async () => {
    if (!user?.companyId) return;

    try {
      setLoading(true);
      const response = await api.getCompanyComments(user.companyId);

      if (response.error) {
        throw new Error(response.error);
      }

      // Ensure response.data is an array
      const commentsData = Array.isArray(response.data)
        ? response.data
        : [response.data].filter(Boolean);

      // Transform the API response to match our Comment type
      const formattedComments: Comment[] = commentsData.map((comment) => {
        const timestamp = (() => {
          try {
            const date = new Date(comment.timestamp);
            return isNaN(date.getTime()) ? new Date() : date;
          } catch {
            return new Date();
          }
        })();

        return {
          id: comment.id,
          userId: comment.userId,
          userName: comment.userName || comment.userId || "Anonymous",
          documentId: comment.documentIdentifier,
          content: comment.comment,
          timestamp,
          position: comment.position,
          resolved: comment.resolved,
          statusText: comment.statusText,
        };
      });

      setCompanyComments(formattedComments);
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error("Error fetching comments:", error);
      if (retryCount < maxRetries) {
        // Exponential backoff for retries
        const delay = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          setRetryCount((prev) => prev + 1);
          fetchComments();
        }, delay);
      } else {
        toast.error("Failed to load comments after multiple attempts");
      }
    } finally {
      setLoading(false);
    }
  }, [user?.companyId, retryCount]);

  // Fetch comments when component mounts or when dependencies change
  useEffect(() => {
    if (isDashboard) {
      fetchComments();
    }
  }, [isDashboard, fetchComments]);

  // Handle external loading prop changes
  useEffect(() => {
    if (externalLoading !== undefined) {
      setLoading(externalLoading);
    }
  }, [externalLoading]);

  // Determine which comments and functions to use
  const commentsToUse =
    useEditorIntegration && editorContext
      ? editorContext.comments
      : isDashboard
        ? companyComments
        : contextComments;

  const addCommentFn =
    useEditorIntegration && editorContext
      ? async (data: {
          content: string;
          position?: { x: number; y: number };
        }) => {
          await editorContext.addComment(data);
        }
      : contextAddComment;

  const resolveCommentFn =
    useEditorIntegration && editorContext
      ? editorContext.resolveComment
      : contextResolveComment;

  const {
    comments: hookComments,
    showResolved,
    toggleShowResolved,
    handleAddComment,
    handleResolveComment,
    isUserLoggedIn,
  } = useComments(commentsToUse, addCommentFn, resolveCommentFn);

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <CommentsHeader
          comments={hookComments}
          showResolved={showResolved}
          toggleShowResolved={toggleShowResolved}
        />
      </CardHeader>

      <CardContent className="flex-1 flex flex-col">
        <div className="space-y-4 flex-1 flex flex-col">
          {!isDashboard && (
            <>
              <CommentForm
                onAddComment={handleAddComment}
                isUserLoggedIn={isUserLoggedIn}
              />
              <Separator />
            </>
          )}

          <div className="flex-1 flex flex-col max-h-[300px] overflow-y-auto">
            <CommentList
              comments={hookComments}
              onResolve={handleResolveComment}
              isUserLoggedIn={isUserLoggedIn}
              loading={loading}
              showResolved={showResolved}
              isDashboard={isDashboard}
              useEditorIntegration={useEditorIntegration}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DocumentComments;
