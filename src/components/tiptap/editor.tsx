"use client";

import { EditorContent, useEditor } from "@tiptap/react";
import { StarterKit } from "@tiptap/starter-kit";

import { TaskItem } from "@tiptap/extension-task-item";
import { TaskList } from "@tiptap/extension-task-list";

import {
  PaginationPlus,
  TableCellPlus,
  TableHeaderPlus,
  TablePlus,
  TableRowPlus,
  type MarginConfig,
  type PaperSize,
} from "./extensions/pagination";

import { Image } from "@tiptap/extension-image";

import { FontFamily } from "@tiptap/extension-font-family";
import { TextStyle } from "@tiptap/extension-text-style";
import { Underline } from "@tiptap/extension-underline";

import { Color } from "@tiptap/extension-color";
import { Highlight } from "@tiptap/extension-highlight";

import { TextAlign } from "@tiptap/extension-text-align";

import CommentExtension from "@sereneinserenade/tiptap-comment-extension";
import { Link } from "@tiptap/extension-link";
import { Placeholder } from "@tiptap/extension-placeholder";

import { FontSizeExtensions } from "@/extensions/font-size";
import { LineHeightExtension } from "@/extensions/line-height";
import { useEditorStore } from "@/store/use-editor-store";
import { useEditorComments } from "@/contexts/EditorCommentsContext";
import { useCallback, useEffect, useRef, useState } from "react";
import { AlignmentHoverMenu } from "./alignment-hover-menu";
import { CommentDialog } from "./comment-dialog";

interface EditorProps {
  initialContent?: string | undefined;
  documentId: string;
}

export const Editor = ({ initialContent, documentId }: EditorProps) => {
  const [isCommentDialogOpen, setIsCommentDialogOpen] = useState(false);
  const [selectedText, setSelectedText] = useState("");

  const { setEditor, setActiveCommentId } = useEditorStore();

  // Get editor comments context (always call hook)
  const editorCommentsContext = useEditorComments();

  const editorRef = useRef<HTMLDivElement>(null);
  const focusCommentWithActiveId = (commentId: string) => {
    // This will be used to scroll to and highlight the comment in the panel
    const commentElement = document.querySelector(
      `[data-comment-id="${commentId}"]`
    );
    if (commentElement) {
      commentElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const handleCommentSubmit = async (
    commentId: string,
    content: string,
    author: string
  ) => {
    if (!editor) return;

    console.log("Applying comment with ID:", commentId);

    // Apply comment to selected text in editor with server-generated ID
    editor.chain().focus().setComment(commentId).run();

    // Check if the comment was applied
    setTimeout(() => {
      const commentElements = document.querySelectorAll(
        `[data-comment-id="${commentId}"]`
      );
      console.log("Comment elements after applying:", commentElements);

      // Also check all comment-highlight elements to see what attributes they have
      const allCommentElements =
        document.querySelectorAll(".comment-highlight");
      console.log("All comment elements after applying:", allCommentElements);
      allCommentElements.forEach((el, index) => {
        console.log(`Element ${index}:`, el.outerHTML);
      });
    }, 100);

    setActiveCommentId(commentId);
  };

  const editor = useEditor({
    immediatelyRender: false,
    content: initialContent || "",

    onCreate({ editor }) {
      console.log("on onCreate updated");
      setEditor(editor);
    },
    onDestroy() {
      console.log("on onDestroy updated");
      setEditor(null);
    },
    onUpdate({ editor }) {
      console.log("on onUpdate updated");
      setEditor(editor);
    },
    onSelectionUpdate({ editor }) {
      console.log("on onSelectionUpdate updated");
      setEditor(editor);
    },
    onTransaction({ editor }) {
      console.log("on onTransaction updated");
      setEditor(editor);
    },
    onFocus({ editor }) {
      console.log("on onFocus updated");
      setEditor(editor);
    },
    onBlur({ editor }) {
      console.log("on onBlur updated");
      setEditor(editor);
    },
    onContentError({ editor }) {
      console.log("on onContentError updated");
      setEditor(editor);
    },
    editorProps: {
      attributes: {
        class: "focus:outline-none bg-black",
      },
    },
    extensions: [
      StarterKit,
      TablePlus.configure({
        resizable: true,
      }),
      TableRowPlus,
      TableHeaderPlus,
      TableCellPlus,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Image,
      Underline,
      FontFamily,
      TextStyle,
      Color,
      LineHeightExtension.configure({
        types: ["heading", "paragraph"],
        defaultLineHeight: "1.5",
      }),
      FontSizeExtensions,
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: "https",
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Placeholder.configure({
        placeholder: "Start typing...",
        emptyEditorClass: "is-editor-empty",
      }),
      PaginationPlus.configure({
        pageHeight: 1123, // A4 height in pixels at 96 DPI (297mm)
        pageGap: 10, // Gap between pages for visual separation
        pageBreakBackground: "#e2e8f0", // Darker gray background for page breaks
        pageHeaderHeight: 0,
        pageGapBorderSize: 1, // Add subtle border
        footerRight: "", // No page numbers
        footerLeft: "", // No page numbers
        headerRight: "", // No headers
        headerLeft: "", // No headers
        defaultMarginConfig: {
          top: 25.4,
          right: 25.4,
          bottom: 25.4,
          left: 25.4,
        }, // Standard A4 margins (1 inch)
        defaultPaperSize: "A4",
      }),
      CommentExtension.configure({
        HTMLAttributes: {
          class: "comment-highlight",
        },
        onCommentActivated: (commentId) => {
          setActiveCommentId(commentId);

          // If editor comments context is available, use it for navigation
          if (editorCommentsContext && commentId) {
            editorCommentsContext.setActiveCommentId(commentId);
          }

          // Only open panel if a comment is actually clicked (commentId exists)
          if (commentId) {
            setTimeout(() => focusCommentWithActiveId(commentId), 100);
          }
        },
      }),
    ],
  });

  const handleCreateComment = useCallback(() => {
    if (!editor) return;

    const { from, to } = editor.state.selection;
    const text = editor.state.doc.textBetween(from, to);

    if (text.trim()) {
      setSelectedText(text);
      setIsCommentDialogOpen(true);
    }
  }, [editor]);

  // Expose createComment function globally so it can be called from hover menu
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).createComment = handleCreateComment;
    }
  }, [handleCreateComment]);

  useEffect(() => {
    if (editor && initialContent) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  return (
    <div>
      <div ref={editorRef} className="relative bg-slate-50 min-h-screen">
        <div className="container mx-auto py-8 bg-[#e2e8f0]">
          <EditorContent editor={editor} />
        </div>
        <AlignmentHoverMenu editorRef={editorRef} />
      </div>
      <CommentDialog
        isOpen={isCommentDialogOpen}
        onClose={() => setIsCommentDialogOpen(false)}
        onSubmit={handleCommentSubmit}
        selectedText={selectedText}
        documentId={documentId}
      />
    </div>
  );
};
