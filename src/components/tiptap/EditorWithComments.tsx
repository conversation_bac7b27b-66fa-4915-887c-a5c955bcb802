import React from "react";
import { EditorCommentsProvider } from "@/contexts/EditorCommentsContext";
import { Editor } from "./editor";
import { CommentsPanel } from "./comments-panel";
import DocumentComments from "../collaboration/DocumentComments";

interface EditorWithCommentsProps {
  initialContent?: string;
  documentId: string;
  showCommentsPanel?: boolean;
  showDocumentComments?: boolean;
}

export const EditorWithComments: React.FC<EditorWithCommentsProps> = ({
  initialContent,
  documentId,
  showCommentsPanel = true,
  showDocumentComments = false,
}) => {
  return (
    <EditorCommentsProvider initialDocumentId={documentId}>
      <div className="flex h-screen">
        {/* Main Editor */}
        <div className="flex-1 relative">
          <Editor initialContent={initialContent} />

          {/* TipTap Comments Panel (right side overlay) */}
          {showCommentsPanel && <CommentsPanel />}
        </div>

        {/* Document Comments Panel (separate column) */}
        {showDocumentComments && (
          <div className="w-80 border-l border-gray-200 bg-white">
            <DocumentComments useEditorIntegration={true} isDashboard={false} />
          </div>
        )}
      </div>
    </EditorCommentsProvider>
  );
};

export default EditorWithComments;
